"""
Memory client utilities for OpenMemory.

This module provides functionality to initialize and manage the Mem0 memory client
with automatic configuration management and Docker environment support.

Docker Ollama Configuration:
When running inside a Docker container and using Ollama as the LLM or embedder provider,
the system automatically detects the Docker environment and adjusts localhost URLs
to properly reach the host machine where <PERSON>llama is running.

Supported Docker host resolution (in order of preference):
1. OLLAMA_HOST environment variable (if set)
2. host.docker.internal (Docker Desktop for Mac/Windows)
3. Docker bridge gateway IP (typically ********** on Linux)
4. Fallback to **********

Example configuration that will be automatically adjusted:
{
    "llm": {
        "provider": "ollama",
        "config": {
            "model": "llama3.1:latest",
            "ollama_base_url": "http://localhost:11434"  # Auto-adjusted in Docker
        }
    }
}
"""

import os
import json
import hashlib
import socket
import platform
import logging
import threading
import time
import queue
import uuid
from typing import Optional, Callable, Any, Tuple

from mem0 import Memory
from app.database import SessionLocal
from app.models import Config as ConfigModel


class MemoryClientSingleton:
    """
    Thread-safe singleton for Mem0 memory client.
    Ensures only one instance exists throughout application lifecycle.
    Supports concurrent operations through operation queuing and background processing.
    Implements graceful degradation for vector store connectivity issues.
    """
    _instance = None
    _lock = threading.Lock()
    _client = None
    _config_hash = None
    _last_health_check = None
    _health_check_interval = 300  # 5 minutes

    # Concurrent operation handling
    _operation_lock = threading.Lock()
    _operation_queue = queue.Queue()
    _worker_thread = None
    _worker_running = False
    _operation_counter = 0
    
    # Graceful degradation features
    _degraded_mode = False
    _operation_backlog = []
    _max_backlog_size = 100
    _last_vector_store_check = None
    _vector_store_check_interval = 60  # 1 minute
    _degradation_reason = None
    _recovery_attempts = 0
    _max_recovery_attempts = 3
    _last_recovery_attempt = None
    _recovery_cooldown = 300  # 5 minutes between recovery attempts
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    # Initialize instance-specific attributes
                    cls._instance._initialize_instance()
        return cls._instance

    def _initialize_instance(self):
        """Initialize instance-specific attributes."""
        # These are already initialized as class variables, but we ensure they exist
        if not hasattr(self.__class__, '_operation_queue'):
            self.__class__._operation_queue = queue.Queue()
        if not hasattr(self.__class__, '_operation_lock'):
            self.__class__._operation_lock = threading.Lock()
        if not hasattr(self.__class__, '_worker_thread'):
            self.__class__._worker_thread = None
        if not hasattr(self.__class__, '_worker_running'):
            self.__class__._worker_running = False
        if not hasattr(self.__class__, '_operation_counter'):
            self.__class__._operation_counter = 0
        
        # Initialize degradation-specific attributes
        if not hasattr(self.__class__, '_degraded_mode'):
            self.__class__._degraded_mode = False
        if not hasattr(self.__class__, '_operation_backlog'):
            self.__class__._operation_backlog = []
        if not hasattr(self.__class__, '_degradation_reason'):
            self.__class__._degradation_reason = None
    
    def _enter_degraded_mode(self, reason: str = "Vector store unavailable"):
        """Enter degraded mode when vector store is unavailable."""
        if not self._degraded_mode:
            logging.warning(f"Entering degraded mode: {reason}")
            print(f"DEGRADED MODE: {reason}")
            self._degraded_mode = True
            self._degradation_reason = reason
            self._last_vector_store_check = time.time()
    
    def _exit_degraded_mode(self):
        """Exit degraded mode when vector store becomes available again."""
        if self._degraded_mode:
            logging.info("Exiting degraded mode: Vector store available")
            print("RECOVERY: Vector store available, exiting degraded mode")
            self._degraded_mode = False
            self._degradation_reason = None
            self._recovery_attempts = 0
            self._process_backlog()
    
    def _process_backlog(self):
        """Process backlogged operations when returning from degraded mode."""
        if not self._operation_backlog:
            return
            
        logging.info(f"Processing {len(self._operation_backlog)} backlogged operations")
        print(f"RECOVERY: Processing {len(self._operation_backlog)} backlogged operations")
        
        processed_count = 0
        failed_count = 0
        
        for operation_data in self._operation_backlog[:]:
            try:
                operation_type = operation_data.get('type')
                content = operation_data.get('content')
                metadata = operation_data.get('metadata', {})
                
                if operation_type == 'add_memory':
                    result = self._client.add(content, metadata=metadata)
                    processed_count += 1
                elif operation_type == 'update_memory':
                    memory_id = operation_data.get('memory_id')
                    result = self._client.update(memory_id, content)
                    processed_count += 1
                else:
                    logging.warning(f"Unknown backlog operation type: {operation_type}")
                    
            except Exception as e:
                failed_count += 1
                logging.error(f"Failed to process backlogged operation: {str(e)}")
        
        # Clear the backlog after processing
        self._operation_backlog = []
        
        if processed_count > 0:
            logging.info(f"Successfully processed {processed_count} backlogged operations")
            print(f"RECOVERY: Successfully processed {processed_count} operations")
        
        if failed_count > 0:
            logging.warning(f"Failed to process {failed_count} backlogged operations")
            print(f"RECOVERY: Failed to process {failed_count} operations")
    
    def _add_to_backlog(self, operation_type: str, content: str, metadata: dict = None, memory_id: str = None):
        """Add operation to backlog for later processing."""
        if len(self._operation_backlog) >= self._max_backlog_size:
            # Remove oldest operation to make room
            removed = self._operation_backlog.pop(0)
            logging.warning(f"Backlog full, removed oldest operation: {removed.get('type')}")
        
        operation_data = {
            'type': operation_type,
            'content': content,
            'metadata': metadata or {},
            'timestamp': time.time()
        }
        
        if memory_id:
            operation_data['memory_id'] = memory_id
        
        self._operation_backlog.append(operation_data)
        logging.info(f"Added {operation_type} operation to backlog (size: {len(self._operation_backlog)})")
    
    def _should_attempt_recovery(self) -> bool:
        """Check if we should attempt recovery from degraded mode."""
        if not self._degraded_mode:
            return False
        
        current_time = time.time()
        
        # Check if enough time has passed since last vector store check
        if (self._last_vector_store_check and 
            current_time - self._last_vector_store_check < self._vector_store_check_interval):
            return False
        
        # Check if we haven't exceeded max recovery attempts
        if self._recovery_attempts >= self._max_recovery_attempts:
            # Check if enough time has passed for cooldown
            if (self._last_recovery_attempt and 
                current_time - self._last_recovery_attempt < self._recovery_cooldown):
                return False
            else:
                # Reset recovery attempts after cooldown
                self._recovery_attempts = 0
        
        return True
    
    def _attempt_vector_store_recovery(self) -> bool:
        """Attempt to recover vector store connectivity."""
        if not self._should_attempt_recovery():
            return False
        
        self._recovery_attempts += 1
        self._last_recovery_attempt = time.time()
        self._last_vector_store_check = time.time()
        
        logging.info(f"Attempting vector store recovery (attempt {self._recovery_attempts}/{self._max_recovery_attempts})")
        print(f"RECOVERY: Attempting vector store recovery (attempt {self._recovery_attempts})")
        
        try:
            if self._client is None:
                return False
            
            # Test vector store connectivity with a lightweight operation
            test_result = self._client.search(query="recovery_test", limit=1)
            
            # If we get here without exception, recovery was successful
            logging.info("Vector store recovery successful")
            print("RECOVERY: Vector store connectivity restored")
            self._exit_degraded_mode()
            return True
            
        except Exception as e:
            logging.warning(f"Vector store recovery attempt {self._recovery_attempts} failed: {str(e)}")
            print(f"RECOVERY: Attempt {self._recovery_attempts} failed: {str(e)}")
            return False
    
    def _store_in_database_only(self, content: str, metadata: dict = None) -> dict:
        """Store memory in database only (fallback when vector store is unavailable)."""
        try:
            # Generate a UUID for the memory
            memory_id = str(uuid.uuid4())
            
            # Create a fallback response that mimics mem0 response format
            fallback_result = {
                'id': memory_id,
                'memory': content,
                'metadata': metadata or {},
                'created_at': time.time(),
                'fallback_mode': True
            }
            
            # In a real implementation, you would store this in your database
            # For now, we'll just return the structured response
            logging.info(f"Stored memory in database-only mode: {memory_id}")
            print(f"DEGRADED: Stored memory in database-only mode")
            
            return {
                'results': [fallback_result]
            }
            
        except Exception as e:
            logging.error(f"Failed to store memory in database-only mode: {str(e)}")
            raise
    
    def add_memory_with_degradation(self, content: str, metadata: dict = None):
        """Add memory with graceful degradation support."""
        try:
            # Check if we should attempt recovery
            if self._degraded_mode:
                self._attempt_vector_store_recovery()
            
            if self._degraded_mode:
                # Store in database only and queue for vector store later
                db_result = self._store_in_database_only(content, metadata)
                self._add_to_backlog('add_memory', content, metadata)
                return db_result
            else:
                # Normal operation - store in both database and vector store
                return self._client.add(content, metadata=metadata)
                
        except Exception as e:
            error_msg = str(e).lower()
            if any(keyword in error_msg for keyword in ['vector store', 'qdrant', 'connection', 'timeout']):
                self._enter_degraded_mode(f"Vector store error: {str(e)}")
                # Retry in degraded mode
                return self.add_memory_with_degradation(content, metadata)
            raise
    
    def update_memory_with_degradation(self, memory_id: str, content: str):
        """Update memory with graceful degradation support."""
        try:
            # Check if we should attempt recovery
            if self._degraded_mode:
                self._attempt_vector_store_recovery()
            
            if self._degraded_mode:
                # Queue for later processing
                self._add_to_backlog('update_memory', content, memory_id=memory_id)
                # Return a fallback response
                return {
                    'results': [{
                        'id': memory_id,
                        'memory': content,
                        'updated_at': time.time(),
                        'fallback_mode': True
                    }]
                }
            else:
                # Normal operation
                return self._client.update(memory_id, content)
                
        except Exception as e:
            error_msg = str(e).lower()
            if any(keyword in error_msg for keyword in ['vector store', 'qdrant', 'connection', 'timeout']):
                self._enter_degraded_mode(f"Vector store error: {str(e)}")
                # Retry in degraded mode
                return self.update_memory_with_degradation(memory_id, content)
            raise
    
    def search_memory_with_degradation(self, query: str, limit: int = 10):
        """Search memory with graceful degradation support."""
        try:
            # Check if we should attempt recovery
            if self._degraded_mode:
                self._attempt_vector_store_recovery()
            
            if self._degraded_mode:
                # In degraded mode, return limited results or cached results
                logging.warning("Search operation in degraded mode - limited functionality")
                print("DEGRADED: Search operation with limited functionality")
                return {
                    'results': [],
                    'degraded_mode': True,
                    'message': 'Vector store unavailable - search functionality limited'
                }
            else:
                # Normal operation
                return self._client.search(query, limit=limit)
                
        except Exception as e:
            error_msg = str(e).lower()
            if any(keyword in error_msg for keyword in ['vector store', 'qdrant', 'connection', 'timeout']):
                self._enter_degraded_mode(f"Vector store error: {str(e)}")
                # Retry in degraded mode
                return self.search_memory_with_degradation(query, limit)
            raise
    
    def get_degradation_status(self) -> dict:
        """Get current degradation status and statistics."""
        return {
            'degraded_mode': self._degraded_mode,
            'degradation_reason': self._degradation_reason,
            'backlog_size': len(self._operation_backlog),
            'max_backlog_size': self._max_backlog_size,
            'recovery_attempts': self._recovery_attempts,
            'max_recovery_attempts': self._max_recovery_attempts,
            'last_recovery_attempt': self._last_recovery_attempt,
            'last_vector_store_check': self._last_vector_store_check,
            'next_recovery_check': (
                self._last_vector_store_check + self._vector_store_check_interval 
                if self._last_vector_store_check else None
            )
        }

    def _is_client_healthy(self) -> bool:
        """
        Check if the current client is healthy and connected.
        Performs comprehensive health validation including vector store connectivity.
        """
        if self._client is None:
            return False
            
        # Skip frequent health checks
        current_time = time.time()
        if (self._last_health_check and 
            current_time - self._last_health_check < self._health_check_interval):
            return True
            
        try:
            # Basic client validation
            if not hasattr(self._client, 'config') or not self._client.config:
                print("Health check failed: Client missing config")
                return False
                
            # Test vector store connectivity with a lightweight operation
            # Try to perform a minimal operation to verify the connection
            try:
                # Attempt to search with empty query to test connectivity
                # This is a lightweight operation that tests the full pipeline
                test_result = self._client.search(query="", limit=1)
                # If we get here without exception, the client is healthy
                self._last_health_check = current_time
                
                # If we were in degraded mode, attempt to exit it
                if self._degraded_mode:
                    self._exit_degraded_mode()
                
                return True
                
            except Exception as vector_error:
                print(f"Vector store health check failed: {vector_error}")
                # Vector store connectivity failed - enter degraded mode
                self._enter_degraded_mode(f"Health check failed: {str(vector_error)}")
                return False
                    
        except Exception as e:
            print(f"Health check failed with error: {e}")
            self._enter_degraded_mode(f"Health check error: {str(e)}")
            
        return False

    def _start_worker(self):
        """Start background worker thread to process queued operations."""
        with self._operation_lock:
            if self._worker_thread is None or not self._worker_thread.is_alive():
                self._worker_running = True
                self._worker_thread = threading.Thread(target=self._process_queue, daemon=True)
                self._worker_thread.start()
                print("Memory operation worker thread started")

    def _stop_worker(self):
        """Stop the background worker thread."""
        with self._operation_lock:
            self._worker_running = False
            if self._worker_thread and self._worker_thread.is_alive():
                # Add a sentinel value to wake up the worker thread
                self._operation_queue.put((None, None, None, None))
                print("Memory operation worker thread stopping...")

    def _process_queue(self):
        """Process operations from the queue."""
        print("Memory operation worker thread started processing")
        while self._worker_running:
            try:
                # Get operation from queue with timeout
                operation, args, kwargs, result_callback = self._operation_queue.get(timeout=1.0)

                # Check for sentinel value (stop signal)
                if operation is None:
                    break

                operation_id = None
                try:
                    # Generate operation ID for tracking
                    with self._operation_lock:
                        self._operation_counter += 1
                        operation_id = self._operation_counter

                    operation_name = getattr(operation, '__name__', str(operation))
                    print(f"Processing queued operation {operation_id}: {operation_name}")

                    # Execute the operation with proper locking
                    with self._operation_lock:
                        result = operation(*args, **kwargs)

                    # Call success callback if provided
                    if result_callback:
                        result_callback(True, result, operation_id)

                    print(f"Queued operation {operation_id} completed successfully")

                except Exception as e:
                    print(f"Queued operation {operation_id} failed: {str(e)}")
                    # Call error callback if provided
                    if result_callback:
                        result_callback(False, str(e), operation_id)
                finally:
                    self._operation_queue.task_done()

            except queue.Empty:
                # Timeout occurred, continue loop to check _worker_running
                continue
            except Exception as e:
                print(f"Worker thread error: {str(e)}")

        print("Memory operation worker thread stopped")

    def _get_critical_config_hash(self, config_dict: dict) -> str:
        """
        Generate hash only for critical configuration parameters that require client reinitialization.
        Critical parameters: vector_store, API keys, provider settings, model configurations.
        """
        critical_config = {}
        
        # Vector store configuration is always critical
        if 'vector_store' in config_dict:
            critical_config['vector_store'] = config_dict['vector_store']
        
        # LLM provider and model configuration
        if 'llm' in config_dict:
            llm_config = config_dict['llm']
            critical_llm = {
                'provider': llm_config.get('provider'),
                'config': {
                    'model': llm_config.get('config', {}).get('model'),
                    'api_key': llm_config.get('config', {}).get('api_key'),
                    'base_url': llm_config.get('config', {}).get('base_url'),
                    'api_version': llm_config.get('config', {}).get('api_version')
                }
            }
            critical_config['llm'] = critical_llm
        
        # Embedder provider and model configuration
        if 'embedder' in config_dict:
            embedder_config = config_dict['embedder']
            critical_embedder = {
                'provider': embedder_config.get('provider'),
                'config': {
                    'model': embedder_config.get('config', {}).get('model'),
                    'api_key': embedder_config.get('config', {}).get('api_key'),
                    'base_url': embedder_config.get('config', {}).get('base_url'),
                    'api_version': embedder_config.get('config', {}).get('api_version')
                }
            }
            critical_config['embedder'] = critical_embedder
        
        # Generate hash from critical config only
        config_str = json.dumps(critical_config, sort_keys=True)
        return hashlib.md5(config_str.encode()).hexdigest()
    
    def _should_reinitialize(self, config_dict: dict) -> bool:
        """
        Determine if client should be reinitialized based on critical config changes.
        Only reinitialize for critical config changes that affect vector store connectivity.
        """
        if self._client is None or self._config_hash is None:
            return True
            
        if not self._is_client_healthy():
            print("Client health check failed, reinitializing...")
            return True
            
        # Only check critical configuration parameters
        new_critical_hash = self._get_critical_config_hash(config_dict)
        
        if self._config_hash != new_critical_hash:
            print(f"Critical config changed (old: {self._config_hash[:8]}, new: {new_critical_hash[:8]}), reinitializing...")
            return True
        else:
            print("Non-critical config change detected, keeping existing client")
            
        return False
    
    def get_client(self, config_dict: dict, custom_instructions: str = None) -> Optional[Memory]:
        """
        Get or initialize the memory client with thread safety.
        
        Args:
            config_dict: Configuration dictionary for the memory client
            custom_instructions: Optional custom instructions
            
        Returns:
            Memory client instance or None if initialization fails
        """
        # First check without lock (performance optimization)
        if not self._should_reinitialize(config_dict):
            return self._client
            
        # Double-checked locking pattern
        with self._lock:
            # Check again inside the lock
            if not self._should_reinitialize(config_dict):
                return self._client
                
            # Generate critical config hash for this initialization
            current_critical_hash = self._get_critical_config_hash(config_dict)
            print(f"Initializing memory client with critical config hash: {current_critical_hash}")
            
            try:
                import signal
                
                def timeout_handler(signum, frame):
                    raise TimeoutError("Memory client initialization timed out")
                
                # Set timeout for initialization
                old_handler = signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(30)  # 30 second timeout
                
                try:
                    new_client = Memory.from_config(config_dict=config_dict)
                    
                    # Only update if initialization was successful
                    self._client = new_client
                    self._config_hash = current_critical_hash  # Store critical config hash
                    self._last_health_check = time.time()
                    
                    print("Memory client initialized successfully")
                    return self._client
                    
                finally:
                    signal.alarm(0)  # Cancel timeout
                    if old_handler:
                        signal.signal(signal.SIGALRM, old_handler)
                        
            except Exception as init_error:
                print(f"Warning: Failed to initialize memory client: {init_error}")
                print("Attempting recovery mechanisms...")
                
                # Attempt recovery if we have an existing client
                if self._client is not None:
                    recovery_success = self._attempt_recovery(config_dict)
                    if recovery_success:
                        print("Recovery successful, using recovered client")
                        return self._client
                
                print("Recovery failed, resetting client")
                # Reset client on failure
                self._client = None
                self._config_hash = None
                self._last_health_check = None
                    
                return None
    
    def _attempt_recovery(self, config_dict: dict) -> bool:
        """
        Attempt to recover a failed or unhealthy client.
        
        Args:
            config_dict: Configuration dictionary for recovery
            
        Returns:
            bool: True if recovery was successful, False otherwise
        """
        if self._client is None:
            return False
            
        try:
            print("Attempting client recovery...")
            
            # First, try a simple health check to see if the issue resolved itself
            if self._is_client_healthy():
                print("Client recovered automatically")
                return True
                
            # Try to reinitialize the vector store connection
            if hasattr(self._client, 'vector_store') and self._client.vector_store:
                try:
                    # Attempt to reconnect the vector store
                    print("Attempting vector store reconnection...")
                    # This is a simplified recovery - in practice, you might need
                    # more specific recovery logic based on the vector store type
                    test_search = self._client.search(query="recovery_test", limit=1)
                    print("Vector store reconnection successful")
                    self._last_health_check = time.time()
                    return True
                except Exception as recovery_error:
                    print(f"Vector store recovery failed: {recovery_error}")
                    
            # If vector store recovery failed, try to recreate the client
            # with a shorter timeout for recovery attempts
            try:
                print("Attempting full client recovery...")
                import signal
                
                def recovery_timeout_handler(signum, frame):
                    raise TimeoutError("Recovery attempt timed out")
                
                old_handler = signal.signal(signal.SIGALRM, recovery_timeout_handler)
                signal.alarm(10)  # Shorter timeout for recovery
                
                try:
                    # Try to create a new client instance
                    recovered_client = Memory.from_config(config_dict=config_dict)
                    self._client = recovered_client
                    self._last_health_check = time.time()
                    print("Full client recovery successful")
                    return True
                    
                finally:
                    signal.alarm(0)
                    if old_handler:
                        signal.signal(signal.SIGALRM, old_handler)
                        
            except Exception as full_recovery_error:
                print(f"Full client recovery failed: {full_recovery_error}")
                
        except Exception as recovery_error:
            print(f"Recovery attempt failed with error: {recovery_error}")
            
        return False
    
    def reset_client(self):
        """
        Force reset of the memory client.
        Use with caution as this will lose vector store context.
        """
        with self._lock:
            print("Force resetting memory client...")

            # Stop worker thread before resetting client
            self._stop_worker()

            self._client = None
            self._config_hash = None
            self._last_health_check = None
    
    def is_healthy(self) -> bool:
        """Check if the memory client is healthy and ready to use."""
        with self._lock:
            return self._client is not None and self._is_client_healthy()
    
    def get_health_status(self) -> dict:
        """
        Get detailed health status information.
        
        Returns:
            dict: Health status with details about client state
        """
        with self._lock:
            status = {
                "healthy": False,
                "client_exists": self._client is not None,
                "last_health_check": self._last_health_check,
                "config_hash": self._config_hash,
                "details": []
            }
            
            if self._client is None:
                status["details"].append("No client instance")
                return status
                
            # Check client configuration
            if not hasattr(self._client, 'config') or not self._client.config:
                status["details"].append("Client missing configuration")
                return status
                
            # Check vector store
            if not hasattr(self._client, 'vector_store') or not self._client.vector_store:
                status["details"].append("Vector store not initialized")
                return status
                
            # Perform connectivity test
            try:
                test_result = self._client.search(query="", limit=1)
                status["healthy"] = True
                status["details"].append("All health checks passed")
            except Exception as e:
                status["details"].append(f"Vector store connectivity failed: {str(e)}")
                
            return status

    def add_memory_async(self, content: str, metadata: dict = None, callback: Callable[[bool, Any, int], None] = None) -> Tuple[bool, str]:
        """
        Queue a memory addition operation for asynchronous processing.

        Args:
            content: Memory content to add
            metadata: Optional metadata for the memory
            callback: Optional callback function called with (success: bool, result: Any, operation_id: int)

        Returns:
            Tuple of (success: bool, message: str)
        """
        if self._client is None:
            return False, "Memory client not initialized"

        try:
            # Start worker thread if not running
            self._start_worker()

            # Queue the operation
            self._operation_queue.put((
                self._client.add,  # The actual mem0 add method
                (content,),        # args
                {"metadata": metadata} if metadata else {},  # kwargs
                callback           # result callback
            ))

            return True, "Memory addition queued for processing"

        except Exception as e:
            return False, f"Failed to queue memory operation: {str(e)}"

    def search_memory_async(self, query: str, limit: int = 10, callback: Callable[[bool, Any, int], None] = None) -> Tuple[bool, str]:
        """
        Queue a memory search operation for asynchronous processing.

        Args:
            query: Search query
            limit: Maximum number of results
            callback: Optional callback function called with (success: bool, result: Any, operation_id: int)

        Returns:
            Tuple of (success: bool, message: str)
        """
        if self._client is None:
            return False, "Memory client not initialized"

        try:
            # Start worker thread if not running
            self._start_worker()

            # Queue the operation
            self._operation_queue.put((
                self._client.search,  # The actual mem0 search method
                (query,),             # args
                {"limit": limit},     # kwargs
                callback              # result callback
            ))

            return True, "Memory search queued for processing"

        except Exception as e:
            return False, f"Failed to queue search operation: {str(e)}"

    def get_operation_queue_status(self) -> dict:
        """
        Get status information about the operation queue.

        Returns:
            dict: Queue status information
        """
        with self._operation_lock:
            return {
                "queue_size": self._operation_queue.qsize(),
                "worker_running": self._worker_running,
                "worker_alive": self._worker_thread.is_alive() if self._worker_thread else False,
                "operation_counter": self._operation_counter
            }

    def wait_for_queue_empty(self, timeout: float = 30.0) -> bool:
        """
        Wait for the operation queue to be empty.

        Args:
            timeout: Maximum time to wait in seconds

        Returns:
            bool: True if queue became empty, False if timeout occurred
        """
        try:
            # Use join with timeout to wait for all queued operations to complete
            import time
            start_time = time.time()

            while time.time() - start_time < timeout:
                if self._operation_queue.empty():
                    return True
                time.sleep(0.1)

            return False

        except Exception as e:
            print(f"Error waiting for queue: {str(e)}")
            return False


# Global singleton instance
_memory_singleton = MemoryClientSingleton()


def _get_config_hash(config_dict):
    """Generate a hash of the config to detect changes."""
    config_str = json.dumps(config_dict, sort_keys=True)
    return hashlib.md5(config_str.encode()).hexdigest()


def _get_docker_host_url():
    """
    Determine the appropriate host URL to reach host machine from inside Docker container.
    Returns the best available option for reaching the host from inside a container.
    """
    # Check for custom environment variable first
    custom_host = os.environ.get('OLLAMA_HOST')
    if custom_host:
        print(f"Using custom Ollama host from OLLAMA_HOST: {custom_host}")
        return custom_host.replace('http://', '').replace('https://', '').split(':')[0]
    
    # Check if we're running inside Docker
    if not os.path.exists('/.dockerenv'):
        # Not in Docker, return localhost as-is
        return "localhost"
    
    print("Detected Docker environment, adjusting host URL for Ollama...")
    
    # Try different host resolution strategies
    host_candidates = []
    
    # 1. host.docker.internal (works on Docker Desktop for Mac/Windows)
    try:
        socket.gethostbyname('host.docker.internal')
        host_candidates.append('host.docker.internal')
        print("Found host.docker.internal")
    except socket.gaierror:
        pass
    
    # 2. Docker bridge gateway (typically ********** on Linux)
    try:
        with open('/proc/net/route', 'r') as f:
            for line in f:
                fields = line.strip().split()
                if fields[1] == '00000000':  # Default route
                    gateway_hex = fields[2]
                    gateway_ip = socket.inet_ntoa(bytes.fromhex(gateway_hex)[::-1])
                    host_candidates.append(gateway_ip)
                    print(f"Found Docker gateway: {gateway_ip}")
                    break
    except (FileNotFoundError, IndexError, ValueError):
        pass
    
    # 3. Fallback to common Docker bridge IP
    if not host_candidates:
        host_candidates.append('**********')
        print("Using fallback Docker bridge IP: **********")
    
    # Return the first available candidate
    return host_candidates[0]


def _fix_ollama_urls(config_section):
    """
    Fix Ollama URLs for Docker environment.
    Replaces localhost URLs with appropriate Docker host URLs.
    Sets default ollama_base_url if not provided.
    """
    if not config_section or "config" not in config_section:
        return config_section
    
    ollama_config = config_section["config"]
    
    # Set default ollama_base_url if not provided
    if "ollama_base_url" not in ollama_config:
        ollama_config["ollama_base_url"] = "http://host.docker.internal:11434"
    else:
        # Check for ollama_base_url and fix if it's localhost
        url = ollama_config["ollama_base_url"]
        if "localhost" in url or "127.0.0.1" in url:
            docker_host = _get_docker_host_url()
            if docker_host != "localhost":
                new_url = url.replace("localhost", docker_host).replace("127.0.0.1", docker_host)
                ollama_config["ollama_base_url"] = new_url
                print(f"Adjusted Ollama URL from {url} to {new_url}")
    
    return config_section


def reset_memory_client():
    """Reset the global memory client to force reinitialization with new config."""
    _memory_singleton.reset_client()


def get_default_memory_config():
    """Get default memory client configuration with sensible defaults."""
    return {
        "vector_store": {
            "provider": "qdrant",
            "config": {
                "collection_name": "openmemory",
                "host": "mem0_store",
                "port": 6333
            }
        },
        "llm": {
            "provider": "openai",
            "config": {
                "model": "gpt-4o-mini",
                "temperature": 0.1,
                "max_tokens": 2000,
                "api_key": "env:OPENAI_API_KEY"
                # Removed timeout and max_retries - not supported by current mem0ai version
            }
        },
        "embedder": {
            "provider": "openai",
            "config": {
                "model": "text-embedding-3-small",
                "api_key": "env:OPENAI_API_KEY"
            }
        },
        "version": "v1.1"
    }


def _parse_environment_variables(config_dict):
    """
    Parse environment variables in config values.
    Converts 'env:VARIABLE_NAME' to actual environment variable values.
    """
    if isinstance(config_dict, dict):
        parsed_config = {}
        for key, value in config_dict.items():
            if isinstance(value, str) and value.startswith("env:"):
                env_var = value.split(":", 1)[1]
                env_value = os.environ.get(env_var)
                if env_value:
                    parsed_config[key] = env_value
                    print(f"Loaded {env_var} from environment for {key}")
                else:
                    print(f"Warning: Environment variable {env_var} not found, keeping original value")
                    parsed_config[key] = value
            elif isinstance(value, dict):
                parsed_config[key] = _parse_environment_variables(value)
            else:
                parsed_config[key] = value
        return parsed_config
    return config_dict


def get_memory_client(custom_instructions: str = None):
    """
    Get or initialize the Mem0 client using thread-safe singleton pattern.

    Args:
        custom_instructions: Optional instructions for the memory project.

    Returns:
        Initialized Mem0 client instance or None if initialization fails.

    Raises:
        Exception: If required API keys are not set or critical configuration is missing.
    """
    try:
        # Start with default configuration
        config = get_default_memory_config()
        
        # Variable to track custom instructions
        db_custom_instructions = None
        
        # Load configuration from database
        try:
            db = SessionLocal()
            db_config = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
            
            if db_config:
                json_config = db_config.value
                
                # Extract custom instructions from openmemory settings
                if "openmemory" in json_config and "custom_instructions" in json_config["openmemory"]:
                    db_custom_instructions = json_config["openmemory"]["custom_instructions"]
                
                # Override defaults with configurations from the database
                if "mem0" in json_config:
                    mem0_config = json_config["mem0"]
                    
                    # Update LLM configuration if available
                    if "llm" in mem0_config and mem0_config["llm"] is not None:
                        config["llm"] = mem0_config["llm"]
                        
                        # Fix Ollama URLs for Docker if needed
                        if config["llm"].get("provider") == "ollama":
                            config["llm"] = _fix_ollama_urls(config["llm"])
                    
                    # Update Embedder configuration if available
                    if "embedder" in mem0_config and mem0_config["embedder"] is not None:
                        config["embedder"] = mem0_config["embedder"]
                        
                        # Fix Ollama URLs for Docker if needed
                        if config["embedder"].get("provider") == "ollama":
                            config["embedder"] = _fix_ollama_urls(config["embedder"])
            else:
                print("No configuration found in database, using defaults")
                    
            db.close()
                            
        except Exception as e:
            print(f"Warning: Error loading configuration from database: {e}")
            print("Using default configuration")
            # Continue with default configuration if database config can't be loaded

        # Use custom_instructions parameter first, then fall back to database value
        instructions_to_use = custom_instructions or db_custom_instructions
        if instructions_to_use:
            config["custom_fact_extraction_prompt"] = instructions_to_use

        # ALWAYS parse environment variables in the final config
        # This ensures that even default config values like "env:OPENAI_API_KEY" get parsed
        print("Parsing environment variables in final config...")
        config = _parse_environment_variables(config)

        # Use the thread-safe singleton to get the client
        return _memory_singleton.get_client(config, custom_instructions)
        
    except Exception as e:
        print(f"Warning: Exception occurred while initializing memory client: {e}")
        print("Server will continue running with limited memory functionality")
        return None


def get_default_user_id():
    return "default_user"
